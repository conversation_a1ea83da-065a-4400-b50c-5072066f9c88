#include "avl.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

/****
  Student1 name: <PERSON>
  Student2 name: <PERSON><PERSON><PERSON>

  Student1 ID: 318407889
  Student2 ID: 325424240
****/

// Submitters function
void submitters(void){
    printf("student1 : <PERSON> \n");
    printf("student1 ID: 318407889 \n");
    printf("student2 : <PERSON><PERSON><PERSON> \n");
    printf("student2 ID: 325424240 \n");
}


// Do not change this. You can use but do not touch.
int _max( int x, int y ){
    return (x < y) ? y : x;
}

// Do not change this. You can use but do not touch.
int _abs( int x ){
    return (x < 0) ? -x : x;
}


// Helper function to update the height of a node
void updateHeight(AVLNodePtr node) {
    if (node) {
        int leftHeight = node->child[LEFT] ? node->child[LEFT]->height : 0;
        int rightHeight = node->child[RIGHT] ? node->child[RIGHT]->height : 0;
        node->height = 1 + _max(leftHeight, rightHeight);
    }
}

// Helper function to get the height of a node
int getHeight(AVLNodePtr node) {
    return node ? node->height : 0;
}

// Helper function to get the balance factor of a node
int getBalance(AVLNodePtr node) {
    return node ? getHeight(node->child[LEFT]) - getHeight(node->child[RIGHT]) : 0;
}

// Right rotate function
AVLNodePtr rightRotate(AVLNodePtr y) {
    AVLNodePtr x = y->child[LEFT];
    AVLNodePtr T2 = x->child[RIGHT];

    x->child[RIGHT] = y;
    y->child[LEFT] = T2;

    updateHeight(y);
    updateHeight(x);

    return x;
}

// Left rotate function
AVLNodePtr leftRotate(AVLNodePtr x) {
    AVLNodePtr y = x->child[RIGHT];
    AVLNodePtr T2 = y->child[LEFT];

    y->child[LEFT] = x;
    x->child[RIGHT] = T2;

    updateHeight(x);
    updateHeight(y);

    return y;
}

// Search function
AVLNodePtr avl_search(AVLNodePtr root, int x, int y) {
    while (root != NULL) {
        if (x < root->x) {
            root = root->child[LEFT];
        } else if (x > root->x) {
            root = root->child[RIGHT];
        } else {
            if (y == root->y) {
                return root;
            } else {
                AVLNodePtr left_result = avl_search(root->child[LEFT], x, y);
                if (left_result != NULL) {
                    return left_result;
                }
                return avl_search(root->child[RIGHT], x, y);
            }
        }
    }
    return NULL;
}

// Insert function
AVLNodePtr avl_insert(AVLNodePtr root, int x, int y) {
    if (root == NULL) {
        return new_avl_node(x, y);
    }

    if (x < root->x || (x == root->x && y < root->y)) {
        root->child[LEFT] = avl_insert(root->child[LEFT], x, y);
        if (root->child[LEFT] != NULL) {
            root->child[LEFT]->parent = root;
        }
    } else if (x > root->x || (x == root->x && y > root->y)) {
        root->child[RIGHT] = avl_insert(root->child[RIGHT], x, y);
        if (root->child[RIGHT] != NULL) {
            root->child[RIGHT]->parent = root;
        }
    } else {
        return root;
    }

    updateHeight(root);
    int balance = getBalance(root);

    // Left Left Case
    if (balance > 1 && (x < root->child[LEFT]->x || (x == root->child[LEFT]->x && y < root->child[LEFT]->y))) {
        return rightRotate(root);
    }

    // Right Right Case
    if (balance < -1 && (x > root->child[RIGHT]->x || (x == root->child[RIGHT]->x && y > root->child[RIGHT]->y))) {
        return leftRotate(root);
    }

    // Left Right Case
    if (balance > 1 && (x > root->child[LEFT]->x || (x == root->child[LEFT]->x && y > root->child[LEFT]->y))) {
        root->child[LEFT] = leftRotate(root->child[LEFT]);
        return rightRotate(root);
    }

    // Right Left Case
    if (balance < -1 && (x < root->child[RIGHT]->x || (x == root->child[RIGHT]->x && y < root->child[RIGHT]->y))) {
        root->child[RIGHT] = rightRotate(root->child[RIGHT]);
        return leftRotate(root);
    }

    return root;
}

// Helper function to find the node with the minimum value
AVLNodePtr minValueNode(AVLNodePtr node) {
    AVLNodePtr current = node;
    while (current->child[LEFT] != NULL) {
        current = current->child[LEFT];
    }
    return current;
}

// Delete function
AVLNodePtr avl_delete(AVLNodePtr root, int x, int y) {
    if (root == NULL) {
        return root;
    }

    if (x < root->x || (x == root->x && y < root->y)) {
        root->child[LEFT] = avl_delete(root->child[LEFT], x, y);
    } else if (x > root->x || (x == root->x && y > root->y)) {
        root->child[RIGHT] = avl_delete(root->child[RIGHT], x, y);
    } else {
        if ((root->child[LEFT] == NULL) || (root->child[RIGHT] == NULL)) {
            AVLNodePtr temp = root->child[LEFT] ? root->child[LEFT] : root->child[RIGHT];
            if (temp == NULL) {
                temp = root;
                root = NULL;
            } else {
                *root = *temp;
            }
            free(temp);
        } else {
            AVLNodePtr temp = minValueNode(root->child[RIGHT]);
            root->x = temp->x;
            root->y = temp->y;
            root->child[RIGHT] = avl_delete(root->child[RIGHT], temp->x, temp->y);
        }
    }

    if (root == NULL) {
        return root;
    }

    updateHeight(root);
    int balance = getBalance(root);

    // Left Left Case
    if (balance > 1 && getBalance(root->child[LEFT]) >= 0) {
        return rightRotate(root);
    }

    // Left Right Case
    if (balance > 1 && getBalance(root->child[LEFT]) < 0) {
        root->child[LEFT] = leftRotate(root->child[LEFT]);
        return rightRotate(root);
    }

    // Right Right Case
    if (balance < -1 && getBalance(root->child[RIGHT]) <= 0) {
        return leftRotate(root);
    }

    // Right Left Case
    if (balance < -1 && getBalance(root->child[RIGHT]) > 0) {
        root->child[RIGHT] = rightRotate(root->child[RIGHT]);
        return leftRotate(root);
    }

    return root;
}

// Join function
AVLNodePtr avl_join(AVLNodePtr root1, AVLNodePtr xnode, AVLNodePtr root2) {
    if (root1 == NULL) {
        xnode->child[RIGHT] = root2;
        if (root2 != NULL) {
            root2->parent = xnode;
        }
        updateHeight(xnode);
        return xnode;
    }

    if (root2 == NULL) {
        xnode->child[LEFT] = root1;
        if (root1 != NULL) {
            root1->parent = xnode;
        }
        updateHeight(xnode);
        return xnode;
    }

    if (getHeight(root1) > getHeight(root2)) {
        AVLNodePtr current = root1;
        while (current->child[RIGHT] != NULL && getHeight(current->child[RIGHT]) >= getHeight(root2)) {
            current = current->child[RIGHT];
        }
        xnode->child[LEFT] = current->child[RIGHT];
        xnode->child[RIGHT] = root2;
        current->child[RIGHT] = xnode;
        xnode->parent = current;

        if (xnode->child[LEFT] != NULL) {
            xnode->child[LEFT]->parent = xnode;
        }
        if (root2 != NULL) {
            root2->parent = xnode;
        }

        updateHeight(xnode);
        updateHeight(current);

        while (current != NULL) {
            updateHeight(current);
            int balance = getBalance(current);

            // Left Left Case
            if (balance > 1 && getBalance(current->child[LEFT]) >= 0) {
                current = rightRotate(current);
            } else if (balance > 1 && getBalance(current->child[LEFT]) < 0) {
                current->child[LEFT] = leftRotate(current->child[LEFT]);
                current = rightRotate(current);
            } else if (balance < -1 && getBalance(current->child[RIGHT]) <= 0) {
                current = leftRotate(current);
            } else if (balance < -1 && getBalance(current->child[RIGHT]) > 0) {
                current->child[RIGHT] = rightRotate(current->child[RIGHT]);
                current = leftRotate(current);
            }
            current = current->parent;
        }
        return root1;
    } else {
        AVLNodePtr current = root2;
        while (current->child[LEFT] != NULL && getHeight(current->child[LEFT]) >= getHeight(root1)) {
            current = current->child[LEFT];
        }
        xnode->child[RIGHT] = current->child[LEFT];
        xnode->child[LEFT] = root1;
        current->child[LEFT] = xnode;
        xnode->parent = current;

        if (xnode->child[RIGHT] != NULL) {
            xnode->child[RIGHT]->parent = xnode;
        }
        if (root1 != NULL) {
            root1->parent = xnode;
        }

        updateHeight(xnode);
        updateHeight(current);

        while (current != NULL) {
            updateHeight(current);
            int balance = getBalance(current);

            // Left Left Case
            if (balance > 1 && getBalance(current->child[LEFT]) >= 0) {
                current = rightRotate(current);
            } else if (balance > 1 && getBalance(current->child[LEFT]) < 0) {
                current->child[LEFT] = leftRotate(current->child[LEFT]);
                current = rightRotate(current);
            } else if (balance < -1 && getBalance(current->child[RIGHT]) <= 0) {
                current = leftRotate(current);
            } else if (balance < -1 && getBalance(current->child[RIGHT]) > 0) {
                current->child[RIGHT] = rightRotate(current->child[RIGHT]);
                current = leftRotate(current);
            }
            current = current->parent;
        }
        return root2;
    }
}

// Split function
AVLNodePtr avl_split(AVLNodePtr root, int key, AVLNodePtr trees_out[2]) {
    if (root == NULL) {
        trees_out[0] = NULL;
        trees_out[1] = NULL;
        return NULL;
    }

    if (key < root->x) {
        avl_split(root->child[LEFT], key, trees_out);
        AVLNodePtr left_tree = trees_out[0];
        AVLNodePtr right_tree = trees_out[1];

        root->child[LEFT] = right_tree;
        if (right_tree != NULL) {
            right_tree->parent = root;
        }
        updateHeight(root);
        trees_out[0] = left_tree;
        trees_out[1] = root;
    } else if (key > root->x) {
        avl_split(root->child[RIGHT], key, trees_out);
        AVLNodePtr left_tree = trees_out[0];
        AVLNodePtr right_tree = trees_out[1];

        root->child[RIGHT] = left_tree;
        if (left_tree != NULL) {
            left_tree->parent = root;
        }
        updateHeight(root);
        trees_out[0] = root;
        trees_out[1] = right_tree;
    } else {
        trees_out[0] = root->child[LEFT];
        trees_out[1] = root->child[RIGHT];
        if (root->child[LEFT] != NULL) {
            root->child[LEFT]->parent = NULL;
        }
        if (root->child[RIGHT] != NULL) {
            root->child[RIGHT]->parent = NULL;
        }
        root->child[LEFT] = NULL;
        root->child[RIGHT] = NULL;
        updateHeight(root);
    }
    return root;
}

// Helper function for range query
double range_query_helper(AVLNodePtr root, int x1, int x2, double max_norm) {
    if (root == NULL) {
        return max_norm;
    }

    if (x1 < root->x) {
        max_norm = range_query_helper(root->child[LEFT], x1, x2, max_norm);
    }

    if (x1 <= root->x && root->x <= x2) {
        double norm = sqrt(root->x * root->x + root->y * root->y);
        if (norm > max_norm) {
            max_norm = norm;
        }
    }

    if (x2 > root->x) {
        max_norm = range_query_helper(root->child[RIGHT], x1, x2, max_norm);
    }

    return max_norm;
}

// Range query function
double range_query(AVLNodePtr root, int x1, int x2) {
    return range_query_helper(root, x1, x2, -1.0);
}

// Create a new AVL node
AVLNodePtr new_avl_node(int x, int y) {
    AVLNodePtr node = (AVLNodePtr)malloc(sizeof(AVLNode));
    if (node == NULL) {
        return NULL;
    }
    node->x = x;
    node->y = y;
    node->height = 1;
    node->child[LEFT] = node->child[RIGHT] = NULL;
    node->parent = NULL;
    return node;
}

// Delete the entire AVL tree
void delete_avl_tree(AVLNodePtr root) {
    if (root == NULL) {
        return;
    }
    delete_avl_tree(root->child[LEFT]);
    delete_avl_tree(root->child[RIGHT]);
    free(root);
}

