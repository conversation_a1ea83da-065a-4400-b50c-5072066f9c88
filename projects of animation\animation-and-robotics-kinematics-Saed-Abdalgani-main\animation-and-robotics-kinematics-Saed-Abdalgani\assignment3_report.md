# Assignment 3 Report: Robot Arm Kinematics
**Name:** <PERSON><PERSON>  
**ID:** 325812667
---

## Task 1: Extend FK to Any Number of Joints

### 1.1 Arbitrary Link Lengths

**Implementation:**

The `SimpleArm` constructor now accepts a `link_lengths` argument, allowing for arbitrary link lengths.  
**How it works:**  
- When creating the arm, you pass a list of link lengths.
- The constructor stores these in `self.link_lengths`.
- The local link vectors (`self.Jl`) are initialized using these lengths.

**Code Example:**
```python
link_lengths = [1.0, 0.3, 0.5, 2.0, 0.5]
arm = SimpleArm(len(link_lengths), link_lengths, joint_limits, joint_types)

class SimpleArm:
    def __init__(self, n=3, link_lengths=1, ...):
        self.n = n
        self.link_lengths = link_lengths
        ...
        for i in range(1, n + 1):
            self.Jl[i, :] = np.array([self.link_lengths[i - 1], 0, 0])
```
**Result:**  
We can now create arms with any link lengths and see the result in the GUI.

![Arbitrary link lengths in GUI](task1.1.png)

---

### 1.2 FK Loop for Any Number of Joints

**Implementation:**

The `FK` (Forward Kinematics) method was rewritten to use a loop, so it works for any number of joints. For each joint, it updates the orientation and position based on the joint's configuration.

**Code Example:**
```python
def FK(self, joint_values=None):
    ...
    for i in range(1, self.n + 1):
        ...
        if joint_type == 'revolute':
            Ri = Rot(joint_value, [0, 0, 1]) @ Ri
            self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i - 1, :]
        elif joint_type == 'prismatic':
            link_vector = np.array([joint_value, 0, 0])
            self.Jw[i, :] = Ri @ link_vector + self.Jw[i - 1, :]
```
**Result:**  
We can now create and pose arms with any number of joints in the GUI.

![FK with any number of joints](task1.2.png)

---

## Task 2: Gradient Descent-based IK (Jacobian Transpose)

### 2.1 VelocityJacobian Implementation and Visualization

**Implementation:**

The `VelocityJacobian` method computes the Jacobian for any number of joints.  
Visualization is done via the `visualize_jacobian` function, which draws arrows for each Jacobian column.

**Code Example:**
```python
def VelocityJacobian(self, joint_values=None):
    ...
    for i in range(self.n):
        # Compute each column of the Jacobian based on the joint's effect
        J[:, i] = ... # Calculation depends on joint configuration

def visualize_jacobian(arm):
    ...
    for i in range(arm.n):
        ...
        jacobian_vectors += vd.Arrow(joint_pos, end_pt, c=color, s=0.005)
```
**Reasoning:**  
Each column of the Jacobian describes how a joint affects the end effector's position.

![Jacobian visualization](task2.1.png)

![Jacobian visualization](task2.11.png)
---

### 2.2 IK with Gradient Descent

**Implementation:**

The `IK` method supports a `gradient_descent` mode using the Jacobian transpose to update joint values in the direction that reduces the error between the end effector and the target.
- The error vector is projected into joint space using the Jacobian transpose.
- Momentum (`velocity` and `beta`) is used for smoother convergence.
- Joint values are clamped to their limits after each update.

**Code Example:**
```python
def IK(self, target, ..., method='gradient_descent', ...):
    ...
    if method == 'gradient_descent':
        grad = J.T @ error
        velocity = beta * velocity + (1 - beta) * grad
        d_joint_values = learning_rate * velocity
    ...
    for i in range(self.n):
        self.joint_values[i] = self.clamp_joint_value(self.joint_values[i] + d_joint_values[i], i)
```
**Robustness:**  
- Joint values are always kept within their allowed limits.
- The use of momentum helps avoid oscillations and speeds up convergence.
![Gradient Descent](task2.2.mp4)
---

### 2.3 IK Demonstration and Out-of-Reach Targets

**Implementation:**

The GUI allows you to place the IK target anywhere. If the target is out of reach, a message is displayed and the solver does not attempt to move the arm.

**Code Example:**
```python
if np.linalg.norm(target) > max_reach:
    error_message = vd.Text2D("Target out of range!", pos='top-middle', s=1.5, c='red', bg='white')
    plt.add(error_message)
    return
```
**Observation:**  
When the target is unreachable, the arm does not move and a warning is shown.

![Target out of range warning](task2.3.mp4)
![Target out of range warning](task2.33.png)

---

## Task 3: Gauss-Newton-based IK (Jacobian Inverse)

### 3.1 Gauss-Newton Option and Iteration Visualization

**Implementation:**

The `IK` method supports a `gauss_newton` mode, which uses the pseudo-inverse of the Jacobian for updates. This method is more direct and often converges faster.
- The error is projected into joint space using the pseudo-inverse of the Jacobian.
- The GUI lets you toggle between methods and see the progression.

**Code Example:**
```python
if method == 'gauss_newton':
    J_transpose = J.T
    JT_J_inv = np.linalg.pinv(J_transpose @ J)
    d_joint_values = JT_J_inv @ J_transpose @ error
```
**Progression:**  
Gauss-Newton typically converges in fewer steps and follows a straighter path to the target compared to gradient descent.

![Gauss-Newton convergence](task3.1newton.mp4)
![gradient descent convergence](task3.1gradiaent.mp4)
---

### 3.2 Redundant Robots and Unique Solutions

**Implementation:**

For redundant robots (more joints than needed), the pseudo-inverse (`np.linalg.pinv`) is used to find the minimum-norm solution, ensuring a unique and stable result.

**Code Example:**
```python
JT_J_inv = np.linalg.pinv(J_transpose @ J)
```
**Result:**  
This approach is standard for handling redundancy in IK and ensures a unique solution.
![Unique Solutions](task3.2.mp4)
---

## Task 4: Extensions

### 4.1 Joint Limits

**Implementation:**

Joint limits can be set interactively using sliders in the GUI. The solver always clamps joint values to these limits after each update.

**Code Example:**
```python
def clamp_joint_value(self, value, joint_idx):
    lower_limit, upper_limit = self.joint_limits[joint_idx]
    return max(lower_limit, min(value, upper_limit))
```
**Result:**  
The arm never exceeds its joint limits, and the user can adjust these limits in real time.

![Joint limits demonstration](task4.1.png)

---

### 4.2 Manipulability Ellipsoid

**Implementation:**

The `calculate_manipulability_ellipsoid` method computes the ellipsoid using the SVD of the Jacobian. The ellipsoid and its axes are drawn at the end effector in the GUI.

**Code Example:**
```python
def calculate_manipulability_ellipsoid(self, scale_factor=0.3):
    J = self.VelocityJacobian()
    U, S, _ = np.linalg.svd(J, full_matrices=False)
    center = self.Jw[-1, :]
    axes = U[:, :3]
    scales = S[:3] * scale_factor
    return center, axes, scales
```
- Drawing is handled in `add_manipulability_objects()` and `draw()`.

**Result:**  
The user can always see the manipulability ellipsoid, which helps understand the arm's dexterity at each pose.

![Manipulability ellipsoid visualization](task4.2.mp4)

---

### 4.3 Prismatic Joints

**Implementation:**

The code supports both revolute and prismatic joints. Prismatic joints are visualized as cubes with arrows indicating their direction. The joint type can be toggled in the GUI.

**How it works:**
- The joint type is stored in `self.joint_types`.
- The GUI button toggles the joint type and updates the display.
- In `draw()`, prismatic joints are shown as cubes with arrows.

**Code Example:**
```python
def set_joint_type(self, joint_idx, joint_type):
    self.joint_types[joint_idx] = joint_type
    ...
```
**Result:**  
The user can switch any joint between revolute and prismatic, and the visualization updates accordingly.

![Prismatic joint visualization](task4.3.mp4)

---

## Summary

All tasks and subtasks have been implemented in my code:

- FK and IK support any number of joints and arbitrary link lengths.
- Joint limits and manipulability ellipsoid are supported and visualized.
- Both gradient descent and Gauss-Newton IK methods are available.
- Prismatic joints are supported and can be toggled in the GUI.

---
