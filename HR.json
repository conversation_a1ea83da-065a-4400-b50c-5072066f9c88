{"name": "HR AI Automation", "flow": [{"id": 2, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": -194, "y": -24}}, "routes": [{"flow": [{"id": 3, "module": "builtin:BasicAggregator", "version": 1, "metadata": {"designer": {"x": 431, "y": -150, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}, {"category": "source", "severity": "error", "message": "Source node is not set."}]}}}, {"id": 4, "module": "airtable:ActionCreateRecord", "version": 3, "metadata": {"designer": {"x": 731, "y": -150, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}]}, {"flow": [{"id": 6, "module": "airtable:ActionGetRecord", "version": 3, "metadata": {"designer": {"x": 431, "y": 150, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}, {"id": 8, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 731, "y": 150}}, "routes": [{"flow": [{"id": 11, "module": "gateway:WebhookRespond", "version": 1, "parameters": {}, "filter": {"name": "Weekends", "conditions": [[{"a": "{{14.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "b": "Sa", "o": "text:equal"}], [{"a": "{{14.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "b": "Su", "o": "text:equal"}]]}, "mapper": {"body": "{\n  \"results\": [\n    {\n      \"toolCallId\": \"{{1.message.toolCalls[].id}}\",\n      \"result\": \"We're actually busy on weekends, could you suggest some other day. As our office hours are from Monday to friday 9am to 5pm.\"\n    }\n  ]\n}", "status": "200", "headers": []}, "metadata": {"designer": {"x": 1031, "y": 0, "messages": [{"category": "reference", "severity": "error", "message": "Module references non-existing module '1'."}]}, "restore": {"expect": {"headers": {"mode": "chose"}}}, "expect": [{"name": "status", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Status", "required": true, "validate": {"min": 100}}, {"name": "body", "type": "any", "label": "Body"}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key", "required": true, "validate": {"max": 256}}, {"name": "value", "type": "text", "label": "Value", "required": true, "validate": {"max": 4096}}], "type": "array", "label": "Custom headers", "validate": {"maxItems": 16}}]}}]}, {"flow": [{"id": 12, "module": "google-calendar:searchEvents", "version": 5, "metadata": {"designer": {"x": 1031, "y": 300, "messages": [{"category": "setupreq", "severity": "error", "message": "The module is not set up."}]}}}, {"id": 13, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 1331, "y": 300}}, "routes": [{"flow": [{"id": 14, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "filter": {"name": "Slots Are Busy", "conditions": [[{"a": "{{5.calendars.`satyama<PERSON><PERSON><EMAIL>`.busy}}", "b": "0", "o": "array:notequal"}]]}, "mapper": {"array": ["{{5.calendars.`satyama<PERSON><PERSON><EMAIL>`.busy}}"]}, "metadata": {"designer": {"x": 1656, "y": 118, "messages": [{"category": "reference", "severity": "error", "message": "Module references non-existing module '5'."}]}, "restore": {"expect": {"array": {"mode": "chose", "items": [null]}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 15, "module": "json:CreateJSON", "version": 1, "parameters": {"type": 94640, "space": ""}, "mapper": {"Value": "{{7.value}}"}, "metadata": {"designer": {"x": 1958, "y": -39, "messages": [{"category": "reference", "severity": "error", "message": "Module references non-existing module '7'."}]}, "restore": {"expect": {"Value": {"mode": "edit"}}, "parameters": {"type": {"label": "Yaseen Mills Function:Availability System"}, "space": {"label": "Empty"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure", "required": true}, {"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "Value", "spec": {"name": "value", "spec": [{"name": "start", "type": "date", "label": null}, {"name": "end", "type": "date", "label": null}], "type": "collection"}, "type": "array", "label": null}]}}, {"id": 16, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 6695054}, "mapper": {"model": "gpt-4o-mini", "top_p": "1", "select": "chat", "messages": [{"role": "assistant", "content": "You will be provided with the busy slots from the clients calender. You need to convert the timezone to {{2.TimeZone}} Timezone. Your task is to list these busy timeslots\n\nRules:\nYou need to double check your answer to make sure your giving the right time as if you don't then bad things will happen.\n\n- If it is a whole hour say for example 9 am. Do not say 9:00 am.\n- If the busy slot goes from 9 am till 5 pm, it means that there are no available slots on this day.\nYou will also send the date.\nYour Output will only tell the busy slots.\nMake sure the output is in single line only\nAlways double check your answer (you are giving wrong information lately)\n\nExample Output:\nNovember 12, 2024: 9 am - 10 am, 1 pm - 3 pm"}, {"role": "user", "content": "These are the busy slots from the clients calender: {{8.json}}\nEach \"start\" and \"end\" pair represents one busy slot.\nOnly output available time, no yapping\n\nExample Output:\nNovember 12, 2024: 9 am - 10 am, 1 pm - 3 pm", "imageDetail": "auto"}], "max_tokens": "350", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 2289, "y": -37, "name": "Convert Into Understandable TimeSlots"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 20, "module": "gateway:WebhookRespond", "version": 1, "parameters": {}, "mapper": {"body": "{\n  \"results\": [\n    {\n      \"toolCallId\": \"{{1.message.toolCalls[].id}}\",\n      \"result\": \"Busy timeslots are {{9.result}}.\"\n    }\n  ]\n}", "status": "200", "headers": []}, "metadata": {"designer": {"x": 2621, "y": -26, "messages": [{"category": "reference", "severity": "error", "message": "Module references non-existing module '1'."}, {"category": "reference", "severity": "error", "message": "Mo<PERSON>le references non-existing module '9'."}]}, "restore": {"expect": {"headers": {"mode": "chose"}}}, "expect": [{"name": "status", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Status", "required": true, "validate": {"min": 100}}, {"name": "body", "type": "any", "label": "Body"}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key", "required": true, "validate": {"max": 256}}, {"name": "value", "type": "text", "label": "Value", "required": true, "validate": {"max": 4096}}], "type": "array", "label": "Custom headers", "validate": {"maxItems": 16}}]}}]}, {"flow": [{"id": 22, "module": "builtin:BasicRouter", "version": 1, "filter": {"name": "All Slots Available", "conditions": [[{"a": "{{5.calendars.`satyama<PERSON><PERSON><EMAIL>`.busy}}", "b": "0", "o": "array:equal"}]]}, "mapper": null, "metadata": {"designer": {"x": 1661, "y": 422}}, "routes": [{"flow": [{"id": 18, "module": "gateway:WebhookRespond", "version": 1, "parameters": {}, "mapper": {"body": "{\n  \"results\": [\n    {\n      \"toolCallId\": \"{{1.message.toolCalls[].id}}\",\n      \"result\": \"All time slots are available between {{4.startTime}} and {{4.endTime}}.\"\n    }\n  ]\n}", "status": "200", "headers": []}, "metadata": {"designer": {"x": 1961, "y": 272, "messages": [{"category": "reference", "severity": "error", "message": "Module references non-existing module '1'."}, {"category": "reference", "severity": "warning", "message": "Referenced module 'Airtable - Create a Record' [4] is not accessible."}]}, "restore": {"expect": {"headers": {"mode": "chose"}}}, "expect": [{"name": "status", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Status", "required": true, "validate": {"min": 100}}, {"name": "body", "type": "any", "label": "Body"}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key", "required": true, "validate": {"max": 256}}, {"name": "value", "type": "text", "label": "Value", "required": true, "validate": {"max": 4096}}], "type": "array", "label": "Custom headers", "validate": {"maxItems": 16}}]}}]}, {"flow": [{"id": 23, "module": "email:ActionSendEmail", "version": 7, "metadata": {"designer": {"x": 1961, "y": 572, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}, {"category": "setup", "severity": "error", "message": "Field must not be empty."}]}}}]}]}]}]}]}]}]}, {"flow": [{"id": 38, "module": "google-forms:getResponse", "version": 2, "metadata": {"designer": {"x": -476, "y": 285, "messages": [{"category": "setupreq", "severity": "error", "message": "The module is not set up."}]}}}, {"id": 36, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": -708, "y": 566}}, "routes": [{"flow": [{"id": 24, "module": "google-forms:getForm", "version": 2, "metadata": {"designer": {"x": 171, "y": 719, "messages": [{"category": "setupreq", "severity": "error", "message": "The module is not set up."}]}}}, {"id": 25, "module": "airtable:ActionCreateRecord", "version": 3, "metadata": {"designer": {"x": 511, "y": 721, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}]}, {"flow": [{"id": 26, "module": "google-forms:getForm", "version": 2, "metadata": {"designer": {"x": 161, "y": 1128}}}, {"id": 28, "module": "airtable:ActionCreateRecord", "version": 3, "metadata": {"designer": {"x": 513, "y": 1123, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}]}, {"flow": [{"id": 27, "module": "google-forms:getForm", "version": 2, "metadata": {"designer": {"x": 163, "y": 1491}}}, {"id": 30, "module": "airtable:ActionCreateRecord", "version": 3, "metadata": {"designer": {"x": 505, "y": 1482, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}]}, {"flow": [{"id": 34, "module": "openai-gpt-3:messageAssistantAdvanced", "version": 1, "metadata": {"designer": {"x": 179, "y": 2218, "messages": [{"category": "setupreq", "severity": "error", "message": "The module is not set up."}]}}}, {"id": 35, "module": "airtable:ActionCreateRecord", "version": 3, "metadata": {"designer": {"x": 525, "y": 2218, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}]}, {"flow": [{"id": 31, "module": "google-forms:getForm", "version": 2, "metadata": {"designer": {"x": 180, "y": 1847}}}, {"id": 32, "module": "airtable:ActionCreateRecord", "version": 3, "metadata": {"designer": {"x": 552, "y": 1843, "messages": [{"category": "setup", "severity": "error", "message": "Value must not be empty."}]}}}]}, {"flow": [{"id": 37, "module": "gateway:WebhookRespond", "version": 1, "metadata": {"designer": {"x": -1087, "y": 570}}}]}]}]}]}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}